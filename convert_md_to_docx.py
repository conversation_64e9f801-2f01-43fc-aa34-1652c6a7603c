#!/usr/bin/env python3
"""
Markdown to Word文档转换器
将车载LED显示屏系统项目方案文档从Markdown格式转换为Word格式
"""

import re
from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.style import WD_STYLE_TYPE
from docx.oxml.shared import OxmlElement, qn

def create_word_document():
    """创建Word文档并设置基本样式"""
    doc = Document()
    
    # 设置文档标题
    title = doc.add_heading('车载LED显示屏系统项目方案', 0)
    title.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    return doc

def add_heading(doc, text, level):
    """添加标题"""
    heading = doc.add_heading(text, level)
    return heading

def add_paragraph(doc, text, style=None):
    """添加段落"""
    if style:
        paragraph = doc.add_paragraph(text, style)
    else:
        paragraph = doc.add_paragraph(text)
    return paragraph

def add_code_block(doc, code_text, language=""):
    """添加代码块"""
    # 创建代码段落
    code_para = doc.add_paragraph()
    code_run = code_para.add_run(code_text)
    
    # 设置代码样式
    font = code_run.font
    font.name = 'Consolas'
    font.size = Pt(9)
    
    # 设置段落样式
    code_para.style = 'Normal'
    
    return code_para

def add_list_item(doc, text, level=0):
    """添加列表项"""
    paragraph = doc.add_paragraph(text, style='List Bullet')
    return paragraph

def parse_markdown_content(content):
    """解析Markdown内容"""
    lines = content.split('\n')
    parsed_content = []
    
    i = 0
    while i < len(lines):
        line = lines[i].strip()
        
        if not line:
            parsed_content.append(('empty', ''))
        elif line.startswith('# '):
            parsed_content.append(('h1', line[2:]))
        elif line.startswith('## '):
            parsed_content.append(('h2', line[3:]))
        elif line.startswith('### '):
            parsed_content.append(('h3', line[4:]))
        elif line.startswith('#### '):
            parsed_content.append(('h4', line[5:]))
        elif line.startswith('```'):
            # 代码块处理
            language = line[3:].strip()
            code_lines = []
            i += 1
            while i < len(lines) and not lines[i].strip().startswith('```'):
                code_lines.append(lines[i])
                i += 1
            parsed_content.append(('code', '\n'.join(code_lines), language))
        elif line.startswith('- ') or line.startswith('* '):
            parsed_content.append(('list', line[2:]))
        elif line.startswith('**') and line.endswith('**'):
            parsed_content.append(('bold', line[2:-2]))
        else:
            parsed_content.append(('paragraph', line))
        
        i += 1
    
    return parsed_content

def convert_markdown_to_docx(md_file_path, docx_file_path):
    """将Markdown文件转换为Word文档"""
    
    # 读取Markdown文件
    with open(md_file_path, 'r', encoding='utf-8') as f:
        md_content = f.read()
    
    # 创建Word文档
    doc = create_word_document()
    
    # 解析Markdown内容
    parsed_content = parse_markdown_content(md_content)
    
    # 转换内容到Word文档
    for item in parsed_content:
        content_type = item[0]
        
        if content_type == 'empty':
            doc.add_paragraph('')
        elif content_type == 'h1':
            add_heading(doc, item[1], 1)
        elif content_type == 'h2':
            add_heading(doc, item[1], 2)
        elif content_type == 'h3':
            add_heading(doc, item[1], 3)
        elif content_type == 'h4':
            add_heading(doc, item[1], 4)
        elif content_type == 'code':
            language = item[2] if len(item) > 2 else ""
            add_code_block(doc, item[1], language)
        elif content_type == 'list':
            add_list_item(doc, item[1])
        elif content_type == 'bold':
            para = doc.add_paragraph()
            run = para.add_run(item[1])
            run.bold = True
        elif content_type == 'paragraph':
            if item[1]:  # 只有非空段落才添加
                # 处理粗体文本
                text = item[1]
                if '**' in text:
                    para = doc.add_paragraph()
                    parts = text.split('**')
                    for i, part in enumerate(parts):
                        if i % 2 == 0:
                            para.add_run(part)
                        else:
                            run = para.add_run(part)
                            run.bold = True
                else:
                    add_paragraph(doc, text)
    
    # 保存Word文档
    doc.save(docx_file_path)
    print(f"转换完成！Word文档已保存为: {docx_file_path}")

def main():
    """主函数"""
    md_file = "车载LED显示屏系统项目方案文档.md"
    docx_file = "车载LED显示屏系统项目方案文档.docx"
    
    try:
        convert_markdown_to_docx(md_file, docx_file)
        print("✅ Markdown到Word转换成功完成！")
        print(f"📄 输入文件: {md_file}")
        print(f"📄 输出文件: {docx_file}")
        
    except FileNotFoundError:
        print(f"❌ 错误：找不到文件 {md_file}")
        print("请确保Markdown文件存在于当前目录中")
        
    except Exception as e:
        print(f"❌ 转换过程中发生错误: {str(e)}")

if __name__ == "__main__":
    main()
