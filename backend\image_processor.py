"""
图像处理模块
负责图像的上传、格式转换、缩放、像素映射等功能
"""
import cv2
import numpy as np
from PIL import Image, ImageEnhance
import os
from typing import Tuple, Optional, Union
import json

class ImageProcessor:
    """图像处理器类"""
    
    def __init__(self, config: dict):
        """
        初始化图像处理器
        
        Args:
            config: LED配置字典
        """
        self.config = config
        self.led_config = config.get('led_display', {})
        
    def load_image(self, image_path: str) -> Optional[np.ndarray]:
        """
        加载图像文件
        
        Args:
            image_path: 图像文件路径
            
        Returns:
            numpy数组格式的图像，如果加载失败返回None
        """
        try:
            # 使用OpenCV加载图像
            image = cv2.imread(image_path)
            if image is None:
                # 尝试使用PIL加载
                pil_image = Image.open(image_path)
                image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
            return image
        except Exception as e:
            print(f"加载图像失败: {e}")
            return None
    
    def resize_image(self, image: np.ndarray, target_width: int, target_height: int, 
                    maintain_aspect: bool = True) -> np.ndarray:
        """
        调整图像尺寸
        
        Args:
            image: 输入图像
            target_width: 目标宽度
            target_height: 目标高度
            maintain_aspect: 是否保持宽高比
            
        Returns:
            调整尺寸后的图像
        """
        if maintain_aspect:
            # 计算缩放比例，保持宽高比
            h, w = image.shape[:2]
            scale = min(target_width / w, target_height / h)
            new_w = int(w * scale)
            new_h = int(h * scale)
            
            # 调整尺寸
            resized = cv2.resize(image, (new_w, new_h), interpolation=cv2.INTER_AREA)
            
            # 创建目标尺寸的黑色背景
            result = np.zeros((target_height, target_width, 3), dtype=np.uint8)
            
            # 计算居中位置
            y_offset = (target_height - new_h) // 2
            x_offset = (target_width - new_w) // 2
            
            # 将调整后的图像放置在中心
            result[y_offset:y_offset+new_h, x_offset:x_offset+new_w] = resized
            
            return result
        else:
            # 直接拉伸到目标尺寸
            return cv2.resize(image, (target_width, target_height), interpolation=cv2.INTER_AREA)
    
    def convert_to_led_format(self, image: np.ndarray, color_mode: str = 'rgb') -> np.ndarray:
        """
        将图像转换为LED显示格式
        
        Args:
            image: 输入图像
            color_mode: 颜色模式 ('rgb', 'grayscale', 'monochrome')
            
        Returns:
            转换后的图像
        """
        if color_mode == 'grayscale':
            # 转换为灰度
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            return cv2.cvtColor(gray, cv2.COLOR_GRAY2BGR)
        elif color_mode == 'monochrome':
            # 转换为单色（黑白）
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            _, binary = cv2.threshold(gray, 128, 255, cv2.THRESH_BINARY)
            return cv2.cvtColor(binary, cv2.COLOR_GRAY2BGR)
        else:
            # RGB模式，直接返回
            return image
    
    def adjust_brightness(self, image: np.ndarray, brightness: float = 1.0) -> np.ndarray:
        """
        调整图像亮度
        
        Args:
            image: 输入图像
            brightness: 亮度系数 (0.0-2.0)
            
        Returns:
            调整亮度后的图像
        """
        # 转换为PIL图像进行亮度调整
        pil_image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
        enhancer = ImageEnhance.Brightness(pil_image)
        enhanced = enhancer.enhance(brightness)
        
        # 转换回OpenCV格式
        return cv2.cvtColor(np.array(enhanced), cv2.COLOR_RGB2BGR)
    
    def create_led_matrix_data(self, image: np.ndarray) -> list:
        """
        创建LED矩阵数据
        
        Args:
            image: 输入图像
            
        Returns:
            LED矩阵数据列表
        """
        h, w = image.shape[:2]
        matrix_data = []
        
        for y in range(h):
            row = []
            for x in range(w):
                # 获取像素值 (BGR格式)
                b, g, r = image[y, x]
                # 转换为RGB格式并添加到矩阵
                row.append([int(r), int(g), int(b)])
            matrix_data.append(row)
        
        return matrix_data
    
    def process_image(self, image_path: str, led_width: int, led_height: int,
                     color_mode: str = 'rgb', brightness: float = 1.0,
                     maintain_aspect: bool = True) -> dict:
        """
        完整的图像处理流程
        
        Args:
            image_path: 图像文件路径
            led_width: LED矩阵宽度
            led_height: LED矩阵高度
            color_mode: 颜色模式
            brightness: 亮度
            maintain_aspect: 是否保持宽高比
            
        Returns:
            处理结果字典
        """
        try:
            # 1. 加载图像
            image = self.load_image(image_path)
            if image is None:
                return {"success": False, "error": "无法加载图像文件"}
            
            # 2. 调整尺寸
            resized_image = self.resize_image(image, led_width, led_height, maintain_aspect)
            
            # 3. 转换颜色模式
            converted_image = self.convert_to_led_format(resized_image, color_mode)
            
            # 4. 调整亮度
            final_image = self.adjust_brightness(converted_image, brightness)
            
            # 5. 创建LED矩阵数据
            matrix_data = self.create_led_matrix_data(final_image)
            
            # 6. 保存处理后的图像
            output_path = image_path.replace('uploads', 'output').replace(
                os.path.splitext(image_path)[1], '_processed.png'
            )
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            cv2.imwrite(output_path, final_image)
            
            return {
                "success": True,
                "matrix_data": matrix_data,
                "processed_image_path": output_path,
                "dimensions": {
                    "width": led_width,
                    "height": led_height
                },
                "settings": {
                    "color_mode": color_mode,
                    "brightness": brightness,
                    "maintain_aspect": maintain_aspect
                }
            }
            
        except Exception as e:
            return {"success": False, "error": f"图像处理失败: {str(e)}"}
    
    def get_image_info(self, image_path: str) -> dict:
        """
        获取图像信息
        
        Args:
            image_path: 图像文件路径
            
        Returns:
            图像信息字典
        """
        try:
            image = self.load_image(image_path)
            if image is None:
                return {"success": False, "error": "无法加载图像"}
            
            h, w = image.shape[:2]
            file_size = os.path.getsize(image_path)
            
            return {
                "success": True,
                "width": w,
                "height": h,
                "channels": image.shape[2] if len(image.shape) > 2 else 1,
                "file_size": file_size,
                "format": os.path.splitext(image_path)[1].upper().replace('.', '')
            }
        except Exception as e:
            return {"success": False, "error": f"获取图像信息失败: {str(e)}"}
