["tests/test_image_processor.py::TestImageProcessor::test_adjust_brightness", "tests/test_image_processor.py::TestImageProcessor::test_convert_to_led_format_grayscale", "tests/test_image_processor.py::TestImageProcessor::test_convert_to_led_format_monochrome", "tests/test_image_processor.py::TestImageProcessor::test_convert_to_led_format_rgb", "tests/test_image_processor.py::TestImageProcessor::test_create_led_matrix_data", "tests/test_image_processor.py::TestImageProcessor::test_get_image_info", "tests/test_image_processor.py::TestImageProcessor::test_process_image_with_file", "tests/test_image_processor.py::TestImageProcessor::test_resize_image_maintain_aspect", "tests/test_image_processor.py::TestImageProcessor::test_resize_image_stretch", "tests/test_led_simulator.py::TestLEDSimulator::test_add_glow_effect", "tests/test_led_simulator.py::TestLEDSimulator::test_add_led_effects", "tests/test_led_simulator.py::TestLEDSimulator::test_add_scan_lines", "tests/test_led_simulator.py::TestLEDSimulator::test_create_led_matrix_image", "tests/test_led_simulator.py::TestLEDSimulator::test_create_led_matrix_image_with_custom_params", "tests/test_led_simulator.py::TestLEDSimulator::test_create_led_pixel", "tests/test_led_simulator.py::TestLEDSimulator::test_create_preview_image", "tests/test_led_simulator.py::TestLEDSimulator::test_empty_matrix", "tests/test_led_simulator.py::TestLEDSimulator::test_get_matrix_info", "tests/test_led_simulator.py::TestLEDSimulator::test_save_led_simulation"]