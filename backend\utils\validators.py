"""
输入验证工具
"""
import os
from typing import Op<PERSON>, Tu<PERSON>, Dict, Any

def validate_image_file(file_path: str, max_size: int = 10 * 1024 * 1024) -> Dict[str, Any]:
    """
    验证图像文件
    
    Args:
        file_path: 文件路径
        max_size: 最大文件大小（字节）
        
    Returns:
        验证结果字典
    """
    result = {"valid": True, "errors": []}
    
    # 检查文件是否存在
    if not os.path.exists(file_path):
        result["valid"] = False
        result["errors"].append("文件不存在")
        return result
    
    # 检查文件大小
    file_size = os.path.getsize(file_path)
    if file_size > max_size:
        result["valid"] = False
        result["errors"].append(f"文件大小超过限制 ({file_size} > {max_size} 字节)")
    
    # 检查文件扩展名
    allowed_extensions = {'.png', '.jpg', '.jpeg', '.gif', '.bmp'}
    file_ext = os.path.splitext(file_path)[1].lower()
    if file_ext not in allowed_extensions:
        result["valid"] = False
        result["errors"].append(f"不支持的文件格式: {file_ext}")
    
    return result

def validate_led_parameters(width: int, height: int, max_width: int = 128, max_height: int = 64) -> Dict[str, Any]:
    """
    验证LED参数
    
    Args:
        width: LED矩阵宽度
        height: LED矩阵高度
        max_width: 最大宽度
        max_height: 最大高度
        
    Returns:
        验证结果字典
    """
    result = {"valid": True, "errors": []}
    
    # 检查宽度
    if not isinstance(width, int) or width < 1:
        result["valid"] = False
        result["errors"].append("宽度必须是正整数")
    elif width > max_width:
        result["valid"] = False
        result["errors"].append(f"宽度超过最大限制 ({width} > {max_width})")
    
    # 检查高度
    if not isinstance(height, int) or height < 1:
        result["valid"] = False
        result["errors"].append("高度必须是正整数")
    elif height > max_height:
        result["valid"] = False
        result["errors"].append(f"高度超过最大限制 ({height} > {max_height})")
    
    return result

def validate_brightness(brightness: float) -> Dict[str, Any]:
    """
    验证亮度参数
    
    Args:
        brightness: 亮度值
        
    Returns:
        验证结果字典
    """
    result = {"valid": True, "errors": []}
    
    if not isinstance(brightness, (int, float)):
        result["valid"] = False
        result["errors"].append("亮度必须是数字")
    elif brightness < 0.1 or brightness > 2.0:
        result["valid"] = False
        result["errors"].append("亮度必须在0.1到2.0之间")
    
    return result

def validate_color_mode(color_mode: str) -> Dict[str, Any]:
    """
    验证颜色模式
    
    Args:
        color_mode: 颜色模式
        
    Returns:
        验证结果字典
    """
    result = {"valid": True, "errors": []}
    
    allowed_modes = {'rgb', 'grayscale', 'monochrome'}
    if color_mode not in allowed_modes:
        result["valid"] = False
        result["errors"].append(f"无效的颜色模式: {color_mode}")
    
    return result

def sanitize_filename(filename: str) -> str:
    """
    清理文件名，移除危险字符
    
    Args:
        filename: 原始文件名
        
    Returns:
        清理后的文件名
    """
    import re
    
    # 移除路径分隔符和其他危险字符
    filename = re.sub(r'[<>:"/\\|?*]', '', filename)
    
    # 限制长度
    if len(filename) > 255:
        name, ext = os.path.splitext(filename)
        filename = name[:255-len(ext)] + ext
    
    return filename

def validate_processing_request(data: dict) -> Dict[str, Any]:
    """
    验证图像处理请求
    
    Args:
        data: 请求数据
        
    Returns:
        验证结果字典
    """
    result = {"valid": True, "errors": []}
    
    # 验证必需字段
    required_fields = ['file_id']
    for field in required_fields:
        if field not in data:
            result["valid"] = False
            result["errors"].append(f"缺少必需字段: {field}")
    
    # 验证LED参数
    if 'led_width' in data and 'led_height' in data:
        led_validation = validate_led_parameters(data['led_width'], data['led_height'])
        if not led_validation["valid"]:
            result["valid"] = False
            result["errors"].extend(led_validation["errors"])
    
    # 验证亮度
    if 'brightness' in data:
        brightness_validation = validate_brightness(data['brightness'])
        if not brightness_validation["valid"]:
            result["valid"] = False
            result["errors"].extend(brightness_validation["errors"])
    
    # 验证颜色模式
    if 'color_mode' in data:
        color_validation = validate_color_mode(data['color_mode'])
        if not color_validation["valid"]:
            result["valid"] = False
            result["errors"].extend(color_validation["errors"])
    
    return result
