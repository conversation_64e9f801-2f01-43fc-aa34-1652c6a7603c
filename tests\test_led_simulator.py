"""
LED模拟器测试
"""
import unittest
import numpy as np
import cv2
import os
import sys
import tempfile

# 添加项目根目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from backend.led_simulator import LEDSimulator
from backend.config import LED_CONFIG

class TestLEDSimulator(unittest.TestCase):
    """LED模拟器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.simulator = LEDSimulator(LED_CONFIG)
        self.test_matrix = self.create_test_matrix()
    
    def create_test_matrix(self):
        """创建测试矩阵数据"""
        # 创建一个简单的8x8矩阵
        matrix = []
        for y in range(8):
            row = []
            for x in range(8):
                if (x + y) % 2 == 0:
                    row.append([255, 0, 0])  # 红色
                else:
                    row.append([0, 255, 0])  # 绿色
            matrix.append(row)
        return matrix
    
    def test_create_led_pixel(self):
        """测试LED像素创建"""
        pixel = self.simulator.create_led_pixel((255, 128, 64), 8, 1.0)
        
        # 检查像素尺寸
        self.assertEqual(pixel.shape, (8, 8, 3))
        
        # 检查像素不为空
        self.assertGreater(np.sum(pixel), 0)
    
    def test_create_led_matrix_image(self):
        """测试LED矩阵图像创建"""
        led_image = self.simulator.create_led_matrix_image(self.test_matrix)
        
        # 检查图像不为空
        self.assertIsNotNone(led_image)
        self.assertGreater(led_image.size, 0)
        
        # 检查图像尺寸合理
        expected_width = 8 * (self.simulator.pixel_size + self.simulator.pixel_spacing) - self.simulator.pixel_spacing
        expected_height = 8 * (self.simulator.pixel_size + self.simulator.pixel_spacing) - self.simulator.pixel_spacing
        
        self.assertEqual(led_image.shape[1], expected_width)
        self.assertEqual(led_image.shape[0], expected_height)
    
    def test_create_led_matrix_image_with_custom_params(self):
        """测试自定义参数的LED矩阵图像创建"""
        led_image = self.simulator.create_led_matrix_image(
            self.test_matrix, 
            pixel_size=10, 
            pixel_spacing=2,
            brightness=0.8
        )
        
        # 检查图像不为空
        self.assertIsNotNone(led_image)
        self.assertGreater(led_image.size, 0)
        
        # 检查图像尺寸
        expected_width = 8 * (10 + 2) - 2
        expected_height = 8 * (10 + 2) - 2
        
        self.assertEqual(led_image.shape[1], expected_width)
        self.assertEqual(led_image.shape[0], expected_height)
    
    def test_add_glow_effect(self):
        """测试发光效果"""
        # 创建基础LED图像
        led_image = self.simulator.create_led_matrix_image(self.test_matrix)
        
        # 添加发光效果
        glow_image = self.simulator._add_glow_effect(led_image)
        
        # 检查图像尺寸不变
        self.assertEqual(glow_image.shape, led_image.shape)
        
        # 检查图像不为空
        self.assertGreater(np.sum(glow_image), 0)
    
    def test_add_scan_lines(self):
        """测试扫描线效果"""
        # 创建基础LED图像
        led_image = self.simulator.create_led_matrix_image(self.test_matrix)
        
        # 添加扫描线效果
        scan_image = self.simulator._add_scan_lines(led_image)
        
        # 检查图像尺寸不变
        self.assertEqual(scan_image.shape, led_image.shape)
        
        # 检查图像不为空
        self.assertGreater(np.sum(scan_image), 0)
    
    def test_add_led_effects(self):
        """测试LED效果组合"""
        # 创建基础LED图像
        led_image = self.simulator.create_led_matrix_image(self.test_matrix)
        
        # 添加所有效果
        effect_image = self.simulator.add_led_effects(
            led_image, 
            glow_effect=True, 
            scan_lines=True
        )
        
        # 检查图像尺寸不变
        self.assertEqual(effect_image.shape, led_image.shape)
        
        # 检查图像不为空
        self.assertGreater(np.sum(effect_image), 0)
    
    def test_create_preview_image(self):
        """测试预览图像创建"""
        preview = self.simulator.create_preview_image(
            self.test_matrix,
            preview_size=(200, 100),
            effects={'brightness': 1.2, 'glow_effect': True}
        )
        
        # 检查预览图像尺寸
        self.assertEqual(preview.shape[:2], (100, 200))
        
        # 检查图像不为空
        self.assertGreater(np.sum(preview), 0)
    
    def test_save_led_simulation(self):
        """测试LED模拟图像保存"""
        with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp_file:
            try:
                # 保存LED模拟图像
                success = self.simulator.save_led_simulation(
                    self.test_matrix,
                    tmp_file.name,
                    effects={'brightness': 1.0, 'glow_effect': True}
                )
                
                # 检查保存成功
                self.assertTrue(success)
                
                # 检查文件存在
                self.assertTrue(os.path.exists(tmp_file.name))
                
                # 检查文件不为空
                self.assertGreater(os.path.getsize(tmp_file.name), 0)
                
                # 尝试读取图像
                saved_image = cv2.imread(tmp_file.name)
                self.assertIsNotNone(saved_image)
                
            finally:
                # 清理临时文件
                if os.path.exists(tmp_file.name):
                    os.unlink(tmp_file.name)
    
    def test_get_matrix_info(self):
        """测试矩阵信息获取"""
        info = self.simulator.get_matrix_info(self.test_matrix)
        
        # 检查基本信息
        self.assertEqual(info['matrix_width'], 8)
        self.assertEqual(info['matrix_height'], 8)
        self.assertEqual(info['total_pixels'], 64)
        
        # 检查输出尺寸计算
        expected_width = 8 * (self.simulator.pixel_size + self.simulator.pixel_spacing) - self.simulator.pixel_spacing
        expected_height = 8 * (self.simulator.pixel_size + self.simulator.pixel_spacing) - self.simulator.pixel_spacing
        
        self.assertEqual(info['output_width'], expected_width)
        self.assertEqual(info['output_height'], expected_height)
    
    def test_empty_matrix(self):
        """测试空矩阵处理"""
        empty_matrix = []
        
        # 测试空矩阵的图像创建
        led_image = self.simulator.create_led_matrix_image(empty_matrix)
        self.assertIsNotNone(led_image)
        
        # 测试空矩阵的信息获取
        info = self.simulator.get_matrix_info(empty_matrix)
        self.assertEqual(info['matrix_width'], 0)
        self.assertEqual(info['matrix_height'], 0)
        self.assertEqual(info['total_pixels'], 0)

if __name__ == '__main__':
    unittest.main()
