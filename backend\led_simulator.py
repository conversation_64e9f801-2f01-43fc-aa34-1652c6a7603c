"""
LED矩阵模拟器
负责生成LED显示效果的模拟图像
"""
import cv2
import numpy as np
from PIL import Image, ImageDraw, ImageFilter
import math
from typing import List, Tuple, Optional

class LEDSimulator:
    """LED矩阵模拟器类"""
    
    def __init__(self, config: dict):
        """
        初始化LED模拟器
        
        Args:
            config: LED配置字典
        """
        self.config = config
        self.led_config = config.get('led_display', {})
        self.pixel_size = self.led_config.get('pixel_size', 8)
        self.pixel_spacing = self.led_config.get('pixel_spacing', 1)
        
    def create_led_pixel(self, color: Tuple[int, int, int], size: int, 
                        brightness: float = 1.0) -> np.ndarray:
        """
        创建单个LED像素
        
        Args:
            color: RGB颜色值
            size: 像素大小
            brightness: 亮度系数
            
        Returns:
            LED像素图像
        """
        # 创建像素画布
        pixel = np.zeros((size, size, 3), dtype=np.uint8)
        
        # 应用亮度
        r, g, b = color
        r = int(r * brightness)
        g = int(g * brightness)
        b = int(b * brightness)
        
        # 限制颜色值范围
        r = max(0, min(255, r))
        g = max(0, min(255, g))
        b = max(0, min(255, b))
        
        # 创建圆形LED效果
        center = size // 2
        radius = size // 2 - 1
        
        for y in range(size):
            for x in range(size):
                distance = math.sqrt((x - center) ** 2 + (y - center) ** 2)
                if distance <= radius:
                    # 计算亮度衰减（中心最亮，边缘较暗）
                    intensity = max(0.3, 1.0 - (distance / radius) * 0.7)
                    pixel[y, x] = [int(b * intensity), int(g * intensity), int(r * intensity)]
        
        return pixel
    
    def create_led_matrix_image(self, matrix_data: List[List[List[int]]], 
                               pixel_size: Optional[int] = None,
                               pixel_spacing: Optional[int] = None,
                               brightness: float = 1.0,
                               background_color: Tuple[int, int, int] = (0, 0, 0)) -> np.ndarray:
        """
        创建LED矩阵显示图像
        
        Args:
            matrix_data: LED矩阵数据 [height][width][rgb]
            pixel_size: LED像素大小
            pixel_spacing: LED像素间距
            brightness: 整体亮度
            background_color: 背景颜色
            
        Returns:
            LED矩阵显示图像
        """
        if not matrix_data or not matrix_data[0]:
            return np.zeros((100, 100, 3), dtype=np.uint8)
        
        # 使用传入参数或默认配置
        pixel_size = pixel_size or self.pixel_size
        pixel_spacing = pixel_spacing or self.pixel_spacing
        
        matrix_height = len(matrix_data)
        matrix_width = len(matrix_data[0])
        
        # 计算输出图像尺寸
        total_pixel_size = pixel_size + pixel_spacing
        output_width = matrix_width * total_pixel_size - pixel_spacing
        output_height = matrix_height * total_pixel_size - pixel_spacing
        
        # 创建输出图像
        output_image = np.full((output_height, output_width, 3), background_color, dtype=np.uint8)
        
        # 绘制每个LED像素
        for y in range(matrix_height):
            for x in range(matrix_width):
                color = matrix_data[y][x]
                
                # 创建LED像素
                led_pixel = self.create_led_pixel(color, pixel_size, brightness)
                
                # 计算像素位置
                start_y = y * total_pixel_size
                end_y = start_y + pixel_size
                start_x = x * total_pixel_size
                end_x = start_x + pixel_size
                
                # 放置LED像素
                output_image[start_y:end_y, start_x:end_x] = led_pixel
        
        return output_image
    
    def add_led_effects(self, image: np.ndarray, glow_effect: bool = True,
                       scan_lines: bool = False) -> np.ndarray:
        """
        添加LED显示效果
        
        Args:
            image: 输入图像
            glow_effect: 是否添加发光效果
            scan_lines: 是否添加扫描线效果
            
        Returns:
            添加效果后的图像
        """
        result = image.copy()
        
        if glow_effect:
            # 添加发光效果
            result = self._add_glow_effect(result)
        
        if scan_lines:
            # 添加扫描线效果
            result = self._add_scan_lines(result)
        
        return result
    
    def _add_glow_effect(self, image: np.ndarray) -> np.ndarray:
        """
        添加发光效果
        
        Args:
            image: 输入图像
            
        Returns:
            添加发光效果后的图像
        """
        # 转换为PIL图像进行模糊处理
        pil_image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
        
        # 创建发光层
        glow = pil_image.filter(ImageFilter.GaussianBlur(radius=2))
        
        # 混合原图和发光层
        result = Image.blend(pil_image, glow, 0.3)
        
        # 转换回OpenCV格式
        return cv2.cvtColor(np.array(result), cv2.COLOR_RGB2BGR)
    
    def _add_scan_lines(self, image: np.ndarray) -> np.ndarray:
        """
        添加扫描线效果
        
        Args:
            image: 输入图像
            
        Returns:
            添加扫描线效果后的图像
        """
        result = image.copy()
        h, w = result.shape[:2]
        
        # 每隔几行添加暗线
        for y in range(0, h, 4):
            result[y:y+1, :] = result[y:y+1, :] * 0.7
        
        return result
    
    def create_preview_image(self, matrix_data: List[List[List[int]]],
                           preview_size: Tuple[int, int] = (400, 200),
                           effects: dict = None) -> np.ndarray:
        """
        创建预览图像
        
        Args:
            matrix_data: LED矩阵数据
            preview_size: 预览图像尺寸
            effects: 效果配置
            
        Returns:
            预览图像
        """
        effects = effects or {}
        
        # 创建LED矩阵图像
        led_image = self.create_led_matrix_image(
            matrix_data,
            brightness=effects.get('brightness', 1.0)
        )
        
        # 添加效果
        if effects.get('glow_effect', True):
            led_image = self.add_led_effects(led_image, glow_effect=True)
        
        # 调整到预览尺寸
        preview_image = cv2.resize(led_image, preview_size, interpolation=cv2.INTER_NEAREST)
        
        return preview_image
    
    def save_led_simulation(self, matrix_data: List[List[List[int]]], 
                           output_path: str, effects: dict = None) -> bool:
        """
        保存LED模拟图像
        
        Args:
            matrix_data: LED矩阵数据
            output_path: 输出文件路径
            effects: 效果配置
            
        Returns:
            是否保存成功
        """
        try:
            effects = effects or {}
            
            # 创建LED矩阵图像
            led_image = self.create_led_matrix_image(
                matrix_data,
                brightness=effects.get('brightness', 1.0)
            )
            
            # 添加效果
            led_image = self.add_led_effects(
                led_image,
                glow_effect=effects.get('glow_effect', True),
                scan_lines=effects.get('scan_lines', False)
            )
            
            # 保存图像
            cv2.imwrite(output_path, led_image)
            return True
            
        except Exception as e:
            print(f"保存LED模拟图像失败: {e}")
            return False
    
    def get_matrix_info(self, matrix_data: List[List[List[int]]]) -> dict:
        """
        获取矩阵信息

        Args:
            matrix_data: LED矩阵数据

        Returns:
            矩阵信息字典
        """
        if not matrix_data:
            return {
                "matrix_width": 0,
                "matrix_height": 0,
                "total_pixels": 0,
                "output_width": 0,
                "output_height": 0,
                "pixel_size": self.pixel_size,
                "pixel_spacing": self.pixel_spacing
            }

        if not matrix_data[0]:
            return {
                "matrix_width": 0,
                "matrix_height": 0,
                "total_pixels": 0,
                "output_width": 0,
                "output_height": 0,
                "pixel_size": self.pixel_size,
                "pixel_spacing": self.pixel_spacing
            }
        
        height = len(matrix_data)
        width = len(matrix_data[0])
        total_pixels = width * height
        
        # 计算输出图像尺寸
        total_pixel_size = self.pixel_size + self.pixel_spacing
        output_width = width * total_pixel_size - self.pixel_spacing
        output_height = height * total_pixel_size - self.pixel_spacing
        
        return {
            "matrix_width": width,
            "matrix_height": height,
            "total_pixels": total_pixels,
            "output_width": output_width,
            "output_height": output_height,
            "pixel_size": self.pixel_size,
            "pixel_spacing": self.pixel_spacing
        }
