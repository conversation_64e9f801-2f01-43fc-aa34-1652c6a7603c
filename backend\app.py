"""
LED显示屏项目主应用
Flask后端API服务
"""
from flask import Flask, request, jsonify, send_file, send_from_directory
from flask_cors import CORS
from flask_socketio import SocketIO, emit
import os
import uuid
from werkzeug.utils import secure_filename
import json
import base64
import cv2
import numpy as np

from config import Config, LED_CONFIG, allowed_file
from image_processor import ImageProcessor
from led_simulator import LEDSimulator

# 创建Flask应用
app = Flask(__name__)
app.config.from_object(Config)

# 启用CORS
CORS(app)

# 启用WebSocket
socketio = SocketIO(app, cors_allowed_origins="*")

# 初始化处理器
image_processor = ImageProcessor(LED_CONFIG)
led_simulator = LEDSimulator(LED_CONFIG)

@app.route('/')
def index():
    """主页面"""
    return send_from_directory('../frontend', 'index.html')

@app.route('/static/<path:filename>')
def static_files(filename):
    """静态文件服务"""
    return send_from_directory('../frontend', filename)

@app.route('/api/config', methods=['GET'])
def get_config():
    """获取LED配置信息"""
    return jsonify({
        "success": True,
        "config": LED_CONFIG
    })

@app.route('/api/upload', methods=['POST'])
def upload_image():
    """上传图像文件"""
    try:
        if 'file' not in request.files:
            return jsonify({"success": False, "error": "没有选择文件"})
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({"success": False, "error": "没有选择文件"})
        
        if file and allowed_file(file.filename):
            # 生成安全的文件名
            filename = secure_filename(file.filename)
            unique_filename = f"{uuid.uuid4()}_{filename}"
            file_path = os.path.join(app.config['UPLOAD_FOLDER'], unique_filename)
            
            # 保存文件
            file.save(file_path)
            
            # 获取图像信息
            image_info = image_processor.get_image_info(file_path)
            
            if image_info["success"]:
                return jsonify({
                    "success": True,
                    "file_id": unique_filename,
                    "file_path": file_path,
                    "image_info": image_info
                })
            else:
                # 删除无效文件
                os.remove(file_path)
                return jsonify(image_info)
        else:
            return jsonify({"success": False, "error": "不支持的文件格式"})
            
    except Exception as e:
        return jsonify({"success": False, "error": f"上传失败: {str(e)}"})

@app.route('/api/process', methods=['POST'])
def process_image():
    """处理图像"""
    try:
        data = request.get_json()
        
        # 获取参数
        file_id = data.get('file_id')
        led_width = data.get('led_width', LED_CONFIG['led_display']['default_width'])
        led_height = data.get('led_height', LED_CONFIG['led_display']['default_height'])
        color_mode = data.get('color_mode', 'rgb')
        brightness = data.get('brightness', 1.0)
        maintain_aspect = data.get('maintain_aspect', True)
        
        # 验证参数
        if not file_id:
            return jsonify({"success": False, "error": "缺少文件ID"})
        
        # 构建文件路径
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], file_id)
        if not os.path.exists(file_path):
            return jsonify({"success": False, "error": "文件不存在"})
        
        # 处理图像
        result = image_processor.process_image(
            file_path, led_width, led_height, color_mode, brightness, maintain_aspect
        )
        
        if result["success"]:
            # 生成LED模拟图像
            matrix_data = result["matrix_data"]
            
            # 创建预览图像
            preview_image = led_simulator.create_preview_image(
                matrix_data,
                preview_size=(400, 200),
                effects={"brightness": brightness, "glow_effect": True}
            )
            
            # 保存预览图像
            preview_filename = f"preview_{file_id}.png"
            preview_path = os.path.join(app.config['OUTPUT_FOLDER'], preview_filename)
            cv2.imwrite(preview_path, preview_image)
            
            # 保存LED模拟图像
            led_filename = f"led_{file_id}.png"
            led_path = os.path.join(app.config['OUTPUT_FOLDER'], led_filename)
            led_simulator.save_led_simulation(
                matrix_data,
                led_path,
                effects={"brightness": brightness, "glow_effect": True}
            )
            
            # 获取矩阵信息
            matrix_info = led_simulator.get_matrix_info(matrix_data)
            
            result.update({
                "preview_path": preview_path,
                "led_simulation_path": led_path,
                "matrix_info": matrix_info
            })
            
            # 通过WebSocket发送实时更新
            socketio.emit('processing_complete', {
                "file_id": file_id,
                "preview_url": f"/api/output/{preview_filename}",
                "led_url": f"/api/output/{led_filename}"
            })
        
        return jsonify(result)
        
    except Exception as e:
        return jsonify({"success": False, "error": f"处理失败: {str(e)}"})

@app.route('/api/preview', methods=['POST'])
def generate_preview():
    """生成实时预览"""
    try:
        data = request.get_json()
        
        # 获取参数
        file_id = data.get('file_id')
        led_width = data.get('led_width', LED_CONFIG['led_display']['default_width'])
        led_height = data.get('led_height', LED_CONFIG['led_display']['default_height'])
        color_mode = data.get('color_mode', 'rgb')
        brightness = data.get('brightness', 1.0)
        maintain_aspect = data.get('maintain_aspect', True)
        
        if not file_id:
            return jsonify({"success": False, "error": "缺少文件ID"})
        
        # 构建文件路径
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], file_id)
        if not os.path.exists(file_path):
            return jsonify({"success": False, "error": "文件不存在"})
        
        # 快速处理生成预览
        result = image_processor.process_image(
            file_path, led_width, led_height, color_mode, brightness, maintain_aspect
        )
        
        if result["success"]:
            matrix_data = result["matrix_data"]
            
            # 生成小尺寸预览
            preview_image = led_simulator.create_preview_image(
                matrix_data,
                preview_size=(200, 100),
                effects={"brightness": brightness}
            )
            
            # 转换为base64
            _, buffer = cv2.imencode('.png', preview_image)
            preview_base64 = base64.b64encode(buffer).decode('utf-8')
            
            return jsonify({
                "success": True,
                "preview_base64": preview_base64,
                "matrix_info": led_simulator.get_matrix_info(matrix_data)
            })
        else:
            return jsonify(result)
            
    except Exception as e:
        return jsonify({"success": False, "error": f"预览生成失败: {str(e)}"})

@app.route('/api/output/<filename>')
def get_output_file(filename):
    """获取输出文件"""
    try:
        return send_from_directory(app.config['OUTPUT_FOLDER'], filename)
    except FileNotFoundError:
        return jsonify({"error": "文件不存在"}), 404

@app.route('/api/download/<file_type>/<file_id>')
def download_file(file_type, file_id):
    """下载文件"""
    try:
        if file_type == 'processed':
            filename = f"processed_{file_id}.png"
            directory = app.config['OUTPUT_FOLDER']
        elif file_type == 'led':
            filename = f"led_{file_id}.png"
            directory = app.config['OUTPUT_FOLDER']
        else:
            return jsonify({"error": "无效的文件类型"}), 400
        
        file_path = os.path.join(directory, filename)
        if os.path.exists(file_path):
            return send_file(file_path, as_attachment=True)
        else:
            return jsonify({"error": "文件不存在"}), 404
            
    except Exception as e:
        return jsonify({"error": f"下载失败: {str(e)}"}), 500

# WebSocket事件处理
@socketio.on('connect')
def handle_connect():
    """客户端连接"""
    print('客户端已连接')
    emit('connected', {'message': '连接成功'})

@socketio.on('disconnect')
def handle_disconnect():
    """客户端断开连接"""
    print('客户端已断开连接')

@socketio.on('request_preview')
def handle_preview_request(data):
    """处理预览请求"""
    try:
        # 这里可以实现实时预览逻辑
        emit('preview_update', {'status': 'processing'})
    except Exception as e:
        emit('error', {'message': str(e)})

if __name__ == '__main__':
    # 确保必要的目录存在
    os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
    os.makedirs(app.config['OUTPUT_FOLDER'], exist_ok=True)
    
    # 启动应用
    print("启动LED显示屏项目服务器...")
    print(f"访问地址: http://localhost:5000")
    socketio.run(app, debug=True, host='0.0.0.0', port=5000)
