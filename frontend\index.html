<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>车载LED显示屏控制系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <!-- 导航栏 -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
            <div class="container-fluid">
                <a class="navbar-brand" href="#">
                    <i class="fas fa-tv me-2"></i>
                    车载LED显示屏控制系统
                </a>
                <div class="navbar-nav ms-auto">
                    <span class="navbar-text" id="connectionStatus">
                        <i class="fas fa-circle text-success"></i> 已连接
                    </span>
                </div>
            </div>
        </nav>

        <div class="row mt-3">
            <!-- 左侧控制面板 -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-upload me-2"></i>图像上传</h5>
                    </div>
                    <div class="card-body">
                        <!-- 文件上传区域 -->
                        <div class="upload-area" id="uploadArea">
                            <div class="upload-content">
                                <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                <p class="text-muted">拖拽图片到此处或点击选择文件</p>
                                <input type="file" id="fileInput" accept="image/*" style="display: none;">
                                <button class="btn btn-primary" onclick="document.getElementById('fileInput').click()">
                                    选择图片
                                </button>
                            </div>
                        </div>
                        
                        <!-- 文件信息 -->
                        <div id="fileInfo" class="mt-3" style="display: none;">
                            <h6>文件信息</h6>
                            <div class="file-info-content">
                                <p><strong>文件名:</strong> <span id="fileName"></span></p>
                                <p><strong>尺寸:</strong> <span id="fileSize"></span></p>
                                <p><strong>格式:</strong> <span id="fileFormat"></span></p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- LED参数配置 -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h5><i class="fas fa-cogs me-2"></i>LED参数配置</h5>
                    </div>
                    <div class="card-body">
                        <!-- 分辨率设置 -->
                        <div class="mb-3">
                            <label class="form-label">LED矩阵分辨率</label>
                            <div class="row">
                                <div class="col-6">
                                    <input type="number" class="form-control" id="ledWidth" 
                                           placeholder="宽度" value="64" min="8" max="128">
                                </div>
                                <div class="col-6">
                                    <input type="number" class="form-control" id="ledHeight" 
                                           placeholder="高度" value="32" min="8" max="64">
                                </div>
                            </div>
                        </div>

                        <!-- 颜色模式 -->
                        <div class="mb-3">
                            <label class="form-label">颜色模式</label>
                            <select class="form-select" id="colorMode">
                                <option value="rgb">RGB全彩</option>
                                <option value="grayscale">灰度</option>
                                <option value="monochrome">单色</option>
                            </select>
                        </div>

                        <!-- 亮度调节 -->
                        <div class="mb-3">
                            <label class="form-label">亮度 (<span id="brightnessValue">100</span>%)</label>
                            <input type="range" class="form-range" id="brightness" 
                                   min="0.1" max="2.0" step="0.1" value="1.0">
                        </div>

                        <!-- 保持宽高比 -->
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="maintainAspect" checked>
                                <label class="form-check-label" for="maintainAspect">
                                    保持图片宽高比
                                </label>
                            </div>
                        </div>

                        <!-- 操作按钮 -->
                        <div class="d-grid gap-2">
                            <button class="btn btn-success" id="processBtn" disabled>
                                <i class="fas fa-play me-2"></i>处理图像
                            </button>
                            <button class="btn btn-info" id="previewBtn" disabled>
                                <i class="fas fa-eye me-2"></i>实时预览
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 下载区域 -->
                <div class="card mt-3" id="downloadCard" style="display: none;">
                    <div class="card-header">
                        <h5><i class="fas fa-download me-2"></i>下载文件</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button class="btn btn-outline-primary" id="downloadProcessed">
                                <i class="fas fa-image me-2"></i>下载处理后图像
                            </button>
                            <button class="btn btn-outline-success" id="downloadLED">
                                <i class="fas fa-tv me-2"></i>下载LED模拟图像
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧显示区域 -->
            <div class="col-md-8">
                <!-- 原图预览 -->
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-image me-2"></i>原始图像</h5>
                    </div>
                    <div class="card-body text-center">
                        <div id="originalImageContainer" class="image-container">
                            <div class="placeholder-content">
                                <i class="fas fa-image fa-4x text-muted"></i>
                                <p class="text-muted mt-3">请上传图像文件</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- LED模拟显示 -->
                <div class="card mt-3">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-tv me-2"></i>LED模拟显示</h5>
                        <div class="led-info" id="ledInfo" style="display: none;">
                            <small class="text-muted">
                                矩阵: <span id="matrixSize"></span> | 
                                像素: <span id="totalPixels"></span>
                            </small>
                        </div>
                    </div>
                    <div class="card-body text-center">
                        <div id="ledDisplayContainer" class="led-container">
                            <div class="placeholder-content">
                                <i class="fas fa-tv fa-4x text-muted"></i>
                                <p class="text-muted mt-3">LED显示效果将在此显示</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 处理状态 -->
                <div class="card mt-3" id="statusCard" style="display: none;">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="spinner-border spinner-border-sm me-3" role="status">
                                <span class="visually-hidden">处理中...</span>
                            </div>
                            <span id="statusText">正在处理图像...</span>
                        </div>
                        <div class="progress mt-2">
                            <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                 role="progressbar" style="width: 100%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast 通知 -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div id="toast" class="toast" role="alert">
            <div class="toast-header">
                <i class="fas fa-info-circle me-2"></i>
                <strong class="me-auto">系统通知</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body" id="toastBody">
                <!-- 通知内容 -->
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.socket.io/4.5.0/socket.io.min.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
