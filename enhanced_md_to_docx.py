#!/usr/bin/env python3
"""
增强版Markdown到Word文档转换器
提供更好的格式化和样式支持
"""

import re
from docx import Document
from docx.shared import Inches, Pt, RGBColor
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.style import WD_STYLE_TYPE
from docx.oxml.shared import OxmlElement, qn
from docx.enum.dml import MSO_THEME_COLOR_INDEX

def setup_document_styles(doc):
    """设置文档样式"""
    
    # 设置正文样式
    styles = doc.styles
    
    # 标题样式
    title_style = styles['Title']
    title_font = title_style.font
    title_font.name = '微软雅黑'
    title_font.size = Pt(24)
    title_font.bold = True
    title_font.color.rgb = RGBColor(0, 51, 102)
    
    # 一级标题样式
    heading1_style = styles['Heading 1']
    heading1_font = heading1_style.font
    heading1_font.name = '微软雅黑'
    heading1_font.size = Pt(18)
    heading1_font.bold = True
    heading1_font.color.rgb = RGBColor(0, 51, 102)
    
    # 二级标题样式
    heading2_style = styles['Heading 2']
    heading2_font = heading2_style.font
    heading2_font.name = '微软雅黑'
    heading2_font.size = Pt(16)
    heading2_font.bold = True
    heading2_font.color.rgb = RGBColor(0, 102, 153)
    
    # 三级标题样式
    heading3_style = styles['Heading 3']
    heading3_font = heading3_style.font
    heading3_font.name = '微软雅黑'
    heading3_font.size = Pt(14)
    heading3_font.bold = True
    heading3_font.color.rgb = RGBColor(51, 102, 153)
    
    # 正文样式
    normal_style = styles['Normal']
    normal_font = normal_style.font
    normal_font.name = '宋体'
    normal_font.size = Pt(12)
    
    # 创建代码样式
    try:
        code_style = styles.add_style('Code', WD_STYLE_TYPE.PARAGRAPH)
        code_font = code_style.font
        code_font.name = 'Consolas'
        code_font.size = Pt(10)
        code_style.paragraph_format.left_indent = Inches(0.5)
        code_style.paragraph_format.space_before = Pt(6)
        code_style.paragraph_format.space_after = Pt(6)
    except:
        pass  # 样式可能已存在

def create_enhanced_document():
    """创建增强的Word文档"""
    doc = Document()
    
    # 设置页面边距
    sections = doc.sections
    for section in sections:
        section.top_margin = Inches(1)
        section.bottom_margin = Inches(1)
        section.left_margin = Inches(1.25)
        section.right_margin = Inches(1.25)
    
    # 设置样式
    setup_document_styles(doc)
    
    # 添加标题页
    title = doc.add_heading('车载LED显示屏系统', 0)
    title.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    subtitle = doc.add_heading('项目方案文档', level=1)
    subtitle.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # 添加空行
    doc.add_paragraph('')
    doc.add_paragraph('')
    
    # 添加项目信息
    info_para = doc.add_paragraph()
    info_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
    info_run = info_para.add_run('版本：v1.0\n编写日期：2025年8月15日\n项目类型：Web应用系统')
    info_run.font.size = Pt(12)
    info_run.font.name = '宋体'
    
    # 添加分页符
    doc.add_page_break()
    
    return doc

def process_markdown_line(doc, line, in_code_block=False):
    """处理单行Markdown内容"""
    line = line.rstrip()
    
    if not line:
        doc.add_paragraph('')
        return False
    
    # 处理标题
    if line.startswith('# '):
        doc.add_heading(line[2:], level=1)
    elif line.startswith('## '):
        doc.add_heading(line[3:], level=2)
    elif line.startswith('### '):
        doc.add_heading(line[4:], level=3)
    elif line.startswith('#### '):
        doc.add_heading(line[5:], level=4)
    # 处理列表
    elif line.startswith('- ') or line.startswith('* '):
        para = doc.add_paragraph(line[2:], style='List Bullet')
    # 处理普通段落
    else:
        if line:
            para = doc.add_paragraph()
            add_formatted_text(para, line)
    
    return False

def add_formatted_text(paragraph, text):
    """添加格式化文本到段落"""
    # 处理粗体文本
    parts = re.split(r'(\*\*.*?\*\*)', text)
    
    for part in parts:
        if part.startswith('**') and part.endswith('**'):
            # 粗体文本
            run = paragraph.add_run(part[2:-2])
            run.bold = True
        elif part.startswith('`') and part.endswith('`'):
            # 内联代码
            run = paragraph.add_run(part[1:-1])
            run.font.name = 'Consolas'
            run.font.size = Pt(10)
        else:
            # 普通文本
            paragraph.add_run(part)

def add_code_block_enhanced(doc, code_content, language=""):
    """添加增强的代码块"""
    # 添加代码块标题（如果有语言标识）
    if language:
        lang_para = doc.add_paragraph(f"代码语言: {language}")
        lang_run = lang_para.runs[0]
        lang_run.font.size = Pt(10)
        lang_run.font.italic = True
    
    # 添加代码内容
    try:
        code_para = doc.add_paragraph(code_content, style='Code')
    except:
        # 如果Code样式不存在，使用普通样式
        code_para = doc.add_paragraph(code_content)
        code_run = code_para.runs[0]
        code_run.font.name = 'Consolas'
        code_run.font.size = Pt(10)
        code_para.paragraph_format.left_indent = Inches(0.5)

def convert_enhanced_markdown_to_docx(md_file_path, docx_file_path):
    """增强版Markdown到Word转换"""
    
    # 读取Markdown文件
    with open(md_file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 创建文档
    doc = create_enhanced_document()
    
    lines = content.split('\n')
    i = 0
    
    while i < len(lines):
        line = lines[i]
        
        # 处理代码块
        if line.strip().startswith('```'):
            language = line.strip()[3:].strip()
            code_lines = []
            i += 1
            
            # 收集代码块内容
            while i < len(lines) and not lines[i].strip().startswith('```'):
                code_lines.append(lines[i])
                i += 1
            
            # 添加代码块
            code_content = '\n'.join(code_lines)
            add_code_block_enhanced(doc, code_content, language)
            
        else:
            # 处理普通行
            process_markdown_line(doc, line)
        
        i += 1
    
    # 保存文档
    doc.save(docx_file_path)
    print(f"增强版转换完成！Word文档已保存为: {docx_file_path}")

def main():
    """主函数"""
    md_file = "车载LED显示屏系统项目方案文档.md"
    docx_file = "车载LED显示屏系统项目方案文档_增强版.docx"
    
    try:
        convert_enhanced_markdown_to_docx(md_file, docx_file)
        print("✅ 增强版Markdown到Word转换成功完成！")
        print(f"📄 输入文件: {md_file}")
        print(f"📄 输出文件: {docx_file}")
        print("🎨 包含增强的格式和样式")
        
    except FileNotFoundError:
        print(f"❌ 错误：找不到文件 {md_file}")
        print("请确保Markdown文件存在于当前目录中")
        
    except Exception as e:
        print(f"❌ 转换过程中发生错误: {str(e)}")

if __name__ == "__main__":
    main()
