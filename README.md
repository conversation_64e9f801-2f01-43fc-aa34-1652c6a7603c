# 车载LED显示屏项目

一个完整的车载LED显示屏系统，支持图像上传、处理和LED矩阵模拟显示。

## 功能特性

- 🖼️ **图像上传与处理**: 支持多种图像格式(PNG, JPG, GIF等)
- 🔄 **智能图像转换**: 自动缩放、颜色转换、像素映射
- 💡 **LED矩阵模拟**: 真实的LED显示效果模拟
- 🎛️ **参数配置**: 可调节分辨率、亮度、颜色深度等
- 👀 **实时预览**: 实时查看LED显示效果
- 🌐 **Web界面**: 直观的用户操作界面

## 技术栈

- **后端**: Python + Flask + OpenCV + Pillow
- **前端**: HTML5 + CSS3 + JavaScript + Bootstrap
- **图像处理**: OpenCV, Pillow, NumPy
- **实时通信**: WebSocket

## 项目结构

```
led-display-project/
├── backend/                 # 后端代码
│   ├── app.py              # Flask主应用
│   ├── image_processor.py  # 图像处理模块
│   ├── led_simulator.py    # LED模拟器
│   ├── config.py           # 配置文件
│   └── utils/              # 工具函数
├── frontend/               # 前端代码
│   ├── index.html          # 主页面
│   ├── css/                # 样式文件
│   ├── js/                 # JavaScript文件
│   └── assets/             # 静态资源
├── uploads/                # 上传文件目录
├── output/                 # 输出文件目录
├── config/                 # 配置文件
├── tests/                  # 测试文件
├── docs/                   # 文档
├── requirements.txt        # Python依赖
└── README.md              # 项目说明
```

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 运行应用

```bash
cd backend
python app.py
```

### 3. 访问应用

打开浏览器访问: http://localhost:5000

## 使用说明

1. 在Web界面上传图像文件
2. 配置LED显示参数（分辨率、亮度等）
3. 查看实时LED模拟效果
4. 下载处理后的图像文件

## 配置说明

LED显示参数可在 `config/led_config.json` 中配置：

- `default_width/height`: 默认LED矩阵尺寸
- `pixel_size`: LED像素大小
- `brightness`: 亮度设置
- `color_depth`: 颜色深度
- `supported_formats`: 支持的图像格式

## 开发文档

详细的开发文档请查看 `docs/` 目录。

## 项目演示

### 演示图像
项目包含一个演示图像 `demo_image.png`，您可以使用它来测试系统功能：

1. 启动应用后访问 http://localhost:5000
2. 上传 `demo_image.png` 文件
3. 调整LED参数（建议64×32分辨率）
4. 点击"处理图像"查看LED模拟效果

### 功能特色
- ✅ 完整的图像上传和处理流程
- ✅ 真实的LED像素模拟效果
- ✅ 实时参数调整和预览
- ✅ 多种颜色模式支持
- ✅ 响应式Web界面设计
- ✅ WebSocket实时通信
- ✅ 完整的测试覆盖

### 技术亮点
- 🔧 模块化架构设计
- 🎨 真实LED发光效果模拟
- ⚡ 高性能图像处理算法
- 📱 跨平台兼容性
- 🔒 输入验证和错误处理
- 📊 详细的系统监控

## 许可证

MIT License
