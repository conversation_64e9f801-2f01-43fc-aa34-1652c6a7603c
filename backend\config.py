"""
LED显示屏项目配置文件
"""
import os
import json
from pathlib import Path

# 项目根目录
PROJECT_ROOT = Path(__file__).parent.parent

# 文件路径配置
UPLOAD_FOLDER = PROJECT_ROOT / 'uploads'
OUTPUT_FOLDER = PROJECT_ROOT / 'output'
CONFIG_FOLDER = PROJECT_ROOT / 'config'
FRONTEND_FOLDER = PROJECT_ROOT / 'frontend'

# 确保目录存在
UPLOAD_FOLDER.mkdir(exist_ok=True)
OUTPUT_FOLDER.mkdir(exist_ok=True)

# Flask配置
class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'led-display-secret-key-2023'
    UPLOAD_FOLDER = str(UPLOAD_FOLDER)
    OUTPUT_FOLDER = str(OUTPUT_FOLDER)
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB max file size

# LED显示配置
def load_led_config():
    """加载LED显示配置"""
    config_path = CONFIG_FOLDER / 'led_config.json'
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        # 默认配置
        return {
            "led_display": {
                "default_width": 64,
                "default_height": 32,
                "max_width": 128,
                "max_height": 64,
                "pixel_size": 8,
                "pixel_spacing": 1,
                "brightness": 255,
                "color_depth": 24,
                "refresh_rate": 60,
                "supported_formats": ["PNG", "JPG", "JPEG", "GIF", "BMP"],
                "max_file_size": 10485760
            }
        }

LED_CONFIG = load_led_config()

# 允许的文件扩展名
ALLOWED_EXTENSIONS = set(ext.lower() for ext in LED_CONFIG['led_display']['supported_formats'])

def allowed_file(filename):
    """检查文件扩展名是否允许"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS
