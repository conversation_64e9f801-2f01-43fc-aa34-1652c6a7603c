/* 车载LED显示屏控制系统样式 */

/* 全局样式 */
body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* 上传区域样式 */
.upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    padding: 40px 20px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    background-color: #f8f9fa;
}

.upload-area:hover {
    border-color: #007bff;
    background-color: #e3f2fd;
}

.upload-area.dragover {
    border-color: #28a745;
    background-color: #d4edda;
    transform: scale(1.02);
}

.upload-content {
    pointer-events: none;
}

/* 图像容器样式 */
.image-container {
    min-height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    background-color: #ffffff;
    overflow: hidden;
}

.image-container img {
    max-width: 100%;
    max-height: 300px;
    object-fit: contain;
    border-radius: 4px;
}

/* LED容器样式 */
.led-container {
    min-height: 250px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    background-color: #000000;
    overflow: hidden;
    position: relative;
}

.led-container img {
    max-width: 100%;
    max-height: 250px;
    object-fit: contain;
    image-rendering: pixelated;
    image-rendering: -moz-crisp-edges;
    image-rendering: crisp-edges;
}

/* LED效果样式 */
.led-pixel {
    display: inline-block;
    width: 8px;
    height: 8px;
    margin: 1px;
    border-radius: 50%;
    box-shadow: 0 0 3px rgba(255, 255, 255, 0.3);
}

/* 占位符内容样式 */
.placeholder-content {
    color: #6c757d;
    text-align: center;
}

/* 文件信息样式 */
.file-info-content {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 6px;
    border-left: 4px solid #007bff;
}

.file-info-content p {
    margin-bottom: 8px;
    font-size: 0.9rem;
}

.file-info-content p:last-child {
    margin-bottom: 0;
}

/* 卡片样式增强 */
.card {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border: none;
    border-radius: 8px;
}

.card-header {
    background-color: #ffffff;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
}

/* 按钮样式增强 */
.btn {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* 表单控件样式 */
.form-control, .form-select {
    border-radius: 6px;
    border: 1px solid #ced4da;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus, .form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* 范围滑块样式 */
.form-range {
    cursor: pointer;
}

.form-range::-webkit-slider-thumb {
    background-color: #007bff;
    border: none;
    border-radius: 50%;
    cursor: pointer;
}

.form-range::-moz-range-thumb {
    background-color: #007bff;
    border: none;
    border-radius: 50%;
    cursor: pointer;
}

/* LED信息样式 */
.led-info {
    font-size: 0.85rem;
    color: #6c757d;
}

/* 状态卡片样式 */
#statusCard {
    border-left: 4px solid #007bff;
}

#statusCard .card-body {
    background-color: #f8f9fa;
}

/* 进度条样式 */
.progress {
    height: 6px;
    border-radius: 3px;
}

.progress-bar {
    border-radius: 3px;
}

/* Toast 通知样式 */
.toast {
    border: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.toast-header {
    background-color: #007bff;
    color: white;
    border-bottom: none;
}

.toast-header .btn-close {
    filter: invert(1);
}

/* 连接状态样式 */
#connectionStatus .fa-circle {
    font-size: 0.8rem;
    margin-right: 5px;
}

#connectionStatus .fa-circle.connected {
    color: #28a745 !important;
}

#connectionStatus .fa-circle.disconnected {
    color: #dc3545 !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container-fluid {
        padding: 10px;
    }
    
    .card {
        margin-bottom: 15px;
    }
    
    .upload-area {
        padding: 30px 15px;
    }
    
    .image-container,
    .led-container {
        min-height: 200px;
    }
    
    .navbar-brand {
        font-size: 1rem;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.3s ease-out;
}

/* LED像素发光效果 */
@keyframes ledGlow {
    0%, 100% {
        box-shadow: 0 0 5px currentColor;
    }
    50% {
        box-shadow: 0 0 10px currentColor, 0 0 15px currentColor;
    }
}

.led-glow {
    animation: ledGlow 2s ease-in-out infinite;
}

/* 加载动画 */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 错误状态样式 */
.error-state {
    border-color: #dc3545 !important;
    background-color: #f8d7da !important;
}

.error-text {
    color: #721c24;
    font-size: 0.875rem;
    margin-top: 5px;
}

/* 成功状态样式 */
.success-state {
    border-color: #28a745 !important;
    background-color: #d1e7dd !important;
}

.success-text {
    color: #0f5132;
    font-size: 0.875rem;
    margin-top: 5px;
}
