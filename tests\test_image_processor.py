"""
图像处理模块测试
"""
import unittest
import numpy as np
import cv2
import os
import sys
import tempfile
from PIL import Image

# 添加项目根目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from backend.image_processor import ImageProcessor
from backend.config import LED_CONFIG

class TestImageProcessor(unittest.TestCase):
    """图像处理器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.processor = ImageProcessor(LED_CONFIG)
        self.test_image = self.create_test_image()
        
    def create_test_image(self):
        """创建测试图像"""
        # 创建一个简单的测试图像
        image = np.zeros((100, 100, 3), dtype=np.uint8)
        
        # 添加一些颜色
        image[20:40, 20:40] = [255, 0, 0]  # 红色方块
        image[60:80, 60:80] = [0, 255, 0]  # 绿色方块
        image[40:60, 40:60] = [0, 0, 255]  # 蓝色方块
        
        return image
    
    def test_resize_image_maintain_aspect(self):
        """测试保持宽高比的图像缩放"""
        resized = self.processor.resize_image(self.test_image, 64, 32, maintain_aspect=True)
        
        # 检查输出尺寸
        self.assertEqual(resized.shape[:2], (32, 64))
        
        # 检查图像不为空
        self.assertGreater(np.sum(resized), 0)
    
    def test_resize_image_stretch(self):
        """测试拉伸图像缩放"""
        resized = self.processor.resize_image(self.test_image, 64, 32, maintain_aspect=False)
        
        # 检查输出尺寸
        self.assertEqual(resized.shape[:2], (32, 64))
    
    def test_convert_to_led_format_rgb(self):
        """测试RGB格式转换"""
        converted = self.processor.convert_to_led_format(self.test_image, 'rgb')
        
        # RGB模式应该返回原图
        np.testing.assert_array_equal(converted, self.test_image)
    
    def test_convert_to_led_format_grayscale(self):
        """测试灰度格式转换"""
        converted = self.processor.convert_to_led_format(self.test_image, 'grayscale')
        
        # 检查输出尺寸
        self.assertEqual(converted.shape, self.test_image.shape)
        
        # 检查是否为灰度（所有通道值相等）
        self.assertTrue(np.allclose(converted[:,:,0], converted[:,:,1]))
        self.assertTrue(np.allclose(converted[:,:,1], converted[:,:,2]))
    
    def test_convert_to_led_format_monochrome(self):
        """测试单色格式转换"""
        converted = self.processor.convert_to_led_format(self.test_image, 'monochrome')
        
        # 检查输出尺寸
        self.assertEqual(converted.shape, self.test_image.shape)
        
        # 检查是否只有0和255值
        unique_values = np.unique(converted)
        self.assertTrue(all(val in [0, 255] for val in unique_values))
    
    def test_adjust_brightness(self):
        """测试亮度调整"""
        # 测试增加亮度
        brighter = self.processor.adjust_brightness(self.test_image, 1.5)
        self.assertEqual(brighter.shape, self.test_image.shape)
        
        # 测试降低亮度
        darker = self.processor.adjust_brightness(self.test_image, 0.5)
        self.assertEqual(darker.shape, self.test_image.shape)
    
    def test_create_led_matrix_data(self):
        """测试LED矩阵数据创建"""
        matrix_data = self.processor.create_led_matrix_data(self.test_image)
        
        # 检查矩阵尺寸
        self.assertEqual(len(matrix_data), self.test_image.shape[0])
        self.assertEqual(len(matrix_data[0]), self.test_image.shape[1])
        
        # 检查数据格式（RGB值）
        for row in matrix_data:
            for pixel in row:
                self.assertEqual(len(pixel), 3)  # RGB三个值
                self.assertTrue(all(0 <= val <= 255 for val in pixel))
    
    def test_process_image_with_file(self):
        """测试完整的图像处理流程"""
        # 创建临时文件
        with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp_file:
            cv2.imwrite(tmp_file.name, self.test_image)
            
            try:
                # 处理图像
                result = self.processor.process_image(
                    tmp_file.name, 32, 16, 'rgb', 1.0, True
                )
                
                # 检查结果
                self.assertTrue(result['success'])
                self.assertIn('matrix_data', result)
                self.assertIn('dimensions', result)
                
                # 检查矩阵数据
                matrix_data = result['matrix_data']
                self.assertEqual(len(matrix_data), 16)  # 高度
                self.assertEqual(len(matrix_data[0]), 32)  # 宽度
                
            finally:
                # 清理临时文件
                os.unlink(tmp_file.name)
    
    def test_get_image_info(self):
        """测试获取图像信息"""
        # 创建临时文件
        with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp_file:
            cv2.imwrite(tmp_file.name, self.test_image)
            
            try:
                # 获取图像信息
                info = self.processor.get_image_info(tmp_file.name)
                
                # 检查结果
                self.assertTrue(info['success'])
                self.assertEqual(info['width'], 100)
                self.assertEqual(info['height'], 100)
                self.assertEqual(info['channels'], 3)
                self.assertEqual(info['format'], 'PNG')
                
            finally:
                # 清理临时文件
                os.unlink(tmp_file.name)

if __name__ == '__main__':
    unittest.main()
