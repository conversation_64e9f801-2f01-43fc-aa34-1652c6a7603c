# ScreenTranslator 屏幕翻译工具项目方案

## 1. 项目概述

### 1.1 项目简介
ScreenTranslator是一款基于Electron框架开发的跨平台屏幕翻译工具，集成了OCR文字识别和多种翻译服务，为用户提供便捷的屏幕内容翻译功能。该项目采用前后端分离的架构设计，支持传统翻译模式和大模型翻译模式，具有良好的扩展性和用户体验。

### 1.2 项目目标
- 提供高效准确的屏幕文字识别功能
- 支持多种翻译服务和翻译模式
- 实现跨平台兼容性（Windows/macOS/Linux）
- 提供友好的用户界面和便捷的快捷键操作
- 支持实时截图区域选择和文字提取
- 集成先进的大语言模型翻译能力

### 1.3 应用场景
- 外语学习和文档翻译
- 国际化软件界面翻译
- 学术论文和技术文档阅读
- 游戏内容本地化
- 多语言网页内容理解

## 2. 技术架构

### 2.1 整体架构设计
项目采用多层架构设计，主要包括：
- **前端展示层**：基于Electron的桌面应用界面
- **业务逻辑层**：主进程控制和IPC通信
- **服务层**：OCR识别服务和翻译服务
- **数据层**：本地配置存储和缓存管理

### 2.2 技术栈选型

#### 前端技术栈
- **Electron 37.2.3**：跨平台桌面应用框架
- **HTML5/CSS3/JavaScript**：用户界面开发
- **Node.js 16+**：运行时环境

#### 后端技术栈
- **Python 3.7+**：OCR服务开发语言
- **Flask**：轻量级Web框架
- **PaddleOCR**：百度开源OCR引擎
- **Pillow**：图像处理库

#### 核心依赖库
- **puppeteer-core**：浏览器自动化控制
- **screenshot-desktop**：屏幕截图功能
- **uiohook-napi**：全局快捷键监听
- **jimp**：图像处理和裁剪
- **express**：Web服务框架
- **chrome-launcher**：Chrome浏览器启动器

### 2.3 架构模式
采用**微服务架构**模式，将不同功能模块解耦：
- OCR识别服务独立部署（端口6987）
- 翻译服务独立运行（端口8888）
- 主应用程序协调各服务交互

## 3. 功能模块详细设计

### 3.1 屏幕截图模块
**功能描述**：实现多显示器环境下的屏幕截图和区域选择

**核心功能**：
- 支持多显示器截图合并
- 实时区域选择和预览
- 截图数据临时存储
- 坐标系统转换和边界计算

**技术实现**：
- 使用`screenshot-desktop`库获取各显示器截图
- 通过`jimp`库进行图像合并和裁剪
- 实现透明覆盖窗口进行区域选择

### 3.2 OCR文字识别模块
**功能描述**：基于PaddleOCR实现高精度文字识别

**核心功能**：
- 支持多语言文字识别（默认英文）
- 返回文字内容、置信度和边界框信息
- 图像预处理和格式转换
- 识别结果结构化输出

**技术实现**：
- Flask Web服务提供OCR API接口
- PaddleOCR引擎进行文字识别
- 支持PNG/JPG等多种图像格式

### 3.3 翻译服务模块
**功能描述**：提供多种翻译模式和翻译源选择

#### 3.3.1 传统翻译模式
- **有道翻译**：通过Puppeteer控制有道翻译网页
- **必应翻译**：通过Puppeteer控制必应翻译网页
- **缓存机制**：避免重复翻译请求
- **结果提取**：DOM元素解析和文本提取

#### 3.3.2 大模型翻译模式
- **智谱GLM-4.1V-flash**：支持视觉理解的大语言模型
- **多模态输入**：同时处理图像和文本
- **结构化输出**：返回原文、翻译和知识点解释
- **API集成**：RESTful API调用和响应处理

### 3.4 用户界面模块
**功能描述**：提供直观友好的桌面应用界面

**核心功能**：
- 主窗口：显示翻译结果和配置选项
- 覆盖窗口：屏幕截图区域选择
- 配置对话框：翻译模式和参数设置
- 实时日志：操作状态和错误信息显示

### 3.5 快捷键监听模块
**功能描述**：实现全局快捷键监听和响应

**核心功能**：
- 默认快捷键：Alt+O触发截图翻译
- 全局监听：后台运行时响应快捷键
- 状态管理：截图模式切换和流程控制
- 鼠标事件：区域选择的开始和结束检测

## 4. 项目结构分析

### 4.1 目录结构
```
fy/
├── app/                    # Electron主应用
│   ├── config/            # 配置文件存储
│   ├── dist/              # 打包输出目录
│   ├── node_modules/      # Node.js依赖
│   ├── uploads/           # 临时文件上传
│   ├── main.js           # Electron主进程
│   ├── preload.js        # 预加载脚本
│   ├── index.html        # 主窗口界面
│   ├── overlay.html      # 覆盖窗口界面
│   ├── tools.js          # 工具函数库
│   ├── fetch.js          # 翻译请求处理
│   ├── puppeteer.js      # 浏览器自动化
│   ├── package.json      # 项目配置文件
│   └── yarn.lock         # 依赖版本锁定
├── paddleocr2/            # OCR服务
│   ├── venv/             # Python虚拟环境
│   ├── uploads/          # OCR图片上传
│   ├── output/           # 服务输出文件
│   └── main.py           # Flask OCR服务
├── README.md             # 英文说明文档
├── CNREADME.md          # 中文说明文档
└── screen*.png          # 项目截图展示
```

### 4.2 核心文件说明

#### 4.2.1 主进程文件（main.js）
- 应用生命周期管理
- 窗口创建和控制
- IPC通信处理
- 配置管理和持久化
- 快捷键监听集成

#### 4.2.2 工具函数库（tools.js）
- 屏幕截图功能实现
- 多显示器支持
- 图像处理和裁剪
- 坐标计算和转换

#### 4.2.3 翻译处理（fetch.js）
- 传统翻译API调用
- 大模型翻译集成
- 图像编码和传输
- 错误处理和重试机制

#### 4.2.4 浏览器自动化（puppeteer.js）
- Chrome浏览器启动和控制
- 翻译网页操作自动化
- 结果提取和缓存
- 并发请求处理

## 5. 关键技术实现方案

### 5.1 多显示器截图合并算法
```javascript
// 计算所有显示器的边界范围
const minX = Math.min(...images.map(i => i.x));
const minY = Math.min(...images.map(i => i.y));
const maxX = Math.max(...images.map(i => i.x + i.width));
const maxY = Math.max(...images.map(i => i.y + i.height));

// 创建合并画布并定位各显示器图像
const merged = new Jimp({ width: totalWidth, height: totalHeight });
for (const item of images) {
    const offsetX = item.x - minX;
    const offsetY = item.y - minY;
    merged.composite(item.img, offsetX, offsetY);
}
```

### 5.2 OCR服务API设计
```python
@app.route('/ocr', methods=['POST'])
def ocr_image():
    # 接收图像文件
    file = request.files['image']
    
    # 保存并处理图像
    image = Image.open(file.stream).convert('RGB')
    image.save(image_path)
    
    # PaddleOCR识别
    results = ocr.ocr(image_path, cls=False)
    
    # 结构化返回结果
    response_data = []
    for line in results:
        for box, (text, score) in line:
            response_data.append({
                'text': text,
                'confidence': round(score, 4),
                'bbox': box
            })
    
    return jsonify({'results': response_data})
```

### 5.3 大模型翻译集成
```javascript
const body = {
    model: "glm-4.1v-thinking-flash",
    messages: [{
        role: "user",
        content: [
            {
                type: "image_url",
                image_url: { url: base64ImageData }
            },
            {
                type: "text",
                text: "提取文字并翻译，返回JSON格式：origin、translate、knowledge"
            }
        ]
    }]
};
```

### 5.4 IPC通信机制
```javascript
// 主进程到渲染进程
mainWindow.webContents.send("show-text", translateObj);

// 渲染进程到主进程
ipcMain.on("sendConfig", (event, options) => {
    // 处理配置更新
});

// 异步调用
ipcMain.handle("getConfig", async () => {
    return configData;
});
```

## 6. 部署和运行说明

### 6.1 环境要求
- **操作系统**：Windows 10+/macOS 10.14+/Ubuntu 18.04+
- **Node.js**：16.0+
- **Python**：3.7+
- **Chrome浏览器**：用于传统翻译模式
- **内存**：建议4GB以上
- **存储空间**：500MB以上

### 6.2 安装步骤

#### 6.2.1 克隆项目
```bash
git clone https://github.com/scriptLin-bjtu/ScreenTranslator.git
cd ScreenTranslator
```

#### 6.2.2 配置Python环境
```bash
cd paddleocr2
python -m venv venv
# Windows
venv\Scripts\activate
# Linux/macOS
source venv/bin/activate
pip install paddlepaddle paddleocr flask pillow
```

#### 6.2.3 安装Node.js依赖
```bash
cd ../app
npm install
# 或使用yarn
yarn install
```

### 6.3 运行应用

#### 6.3.1 启动OCR服务
```bash
cd paddleocr2
python main.py
# 服务将在 http://localhost:6987 启动
```

#### 6.3.2 启动主应用
```bash
cd app
npm start
# 或
yarn start
```

### 6.4 生产环境打包
```bash
cd app
npm run dist
# 打包文件将生成在 dist/ 目录
```

### 6.5 配置说明

#### 6.5.1 传统翻译模式配置
- 确保系统安装Chrome浏览器
- 配置OCR服务文件路径
- 选择翻译源（有道/必应）

#### 6.5.2 大模型翻译模式配置
- 注册智谱清言开发者账号
- 获取API Key
- 在应用中配置API密钥

## 7. 项目特色和优势

### 7.1 技术特色
- **跨平台兼容**：基于Electron实现一次开发多平台运行
- **模块化设计**：服务解耦，便于维护和扩展
- **多模态翻译**：支持传统翻译和AI大模型翻译
- **高精度OCR**：集成百度PaddleOCR，识别准确率高
- **实时交互**：快捷键触发，操作便捷高效

### 7.2 用户体验优势
- **界面友好**：简洁直观的用户界面设计
- **操作便捷**：一键截图翻译，流程简化
- **结果丰富**：提供原文、翻译和知识点解释
- **配置灵活**：支持多种翻译模式和参数调整
- **响应迅速**：本地OCR服务，处理速度快

### 7.3 扩展性优势
- **插件化架构**：易于添加新的翻译服务
- **API标准化**：服务接口规范，便于集成
- **配置外置**：支持动态配置修改
- **缓存优化**：避免重复请求，提升性能

## 8. 技术实现参考代码

### 8.1 屏幕截图和区域选择核心代码
```javascript
// tools.js - 截图监听和处理
function moniter(openfunc, closefunc, processfunc, alert, key = UiohookKey.O) {
    uIOhook.on("keydown", (e) => {
        if (e.keycode === key && e.altKey) {
            cutmode = !cutmode;
            cutmode ? openfunc() : closefunc();
            alert("截屏模式:" + cutmode);
        }
    });
    
    uIOhook.on("mouseup", async (e) => {
        if (cutmode && waitend) {
            await cutAndMerge({
                x: Math.min(startPoint.x, endPoint.x),
                y: Math.min(startPoint.y, endPoint.y),
                width: Math.abs(endPoint.x - startPoint.x),
                height: Math.abs(endPoint.y - startPoint.y)
            });
            await processfunc();
        }
    });
}
```

### 8.2 OCR服务核心实现
```python
# paddleocr2/main.py - OCR识别服务
from paddleocr import PaddleOCR
from flask import Flask, request, jsonify

ocr = PaddleOCR(use_angle_cls=False, lang='en')
app = Flask(__name__)

@app.route('/ocr', methods=['POST'])
def ocr_image():
    file = request.files['image']
    image = Image.open(file.stream).convert('RGB')
    image.save(image_path)
    
    results = ocr.ocr(image_path, cls=False)
    response_data = []
    for line in results:
        for box, (text, score) in line:
            response_data.append({
                'text': text,
                'confidence': round(score, 4),
                'bbox': box
            })
    
    return jsonify({'results': response_data})
```

### 8.3 大模型翻译实现
```javascript
// fetch.js - VLM翻译功能
async function VLMtranslate(alert) {
    const imageBuffer = await fs.readFile(imagePath);
    const base64ImageData = `data:image/png;base64,${imageBuffer.toString('base64')}`;
    
    const body = {
        model: "glm-4.1v-thinking-flash",
        messages: [{
            role: "user",
            content: [
                { type: "image_url", image_url: { url: base64ImageData } },
                { 
                    type: "text", 
                    text: "提取文字信息，返回json格式：origin(原文)、translate(中文翻译)、knowledge(重点解释)"
                }
            ]
        }]
    };
    
    const response = await fetch(url, {
        method: "POST",
        headers: {
            Authorization: "Bearer " + apikey,
            "Content-Type": "application/json"
        },
        body: JSON.stringify(body)
    });
    
    const data = await response.json();
    return JSON.parse(data.choices[0].message.content);
}
```

### 8.4 Puppeteer自动化翻译
```javascript
// puppeteer.js - 浏览器自动化翻译
async function translateText(content, source) {
    if (source == "youdao") {
        await pageYD.evaluate((text) => {
            const inputDiv = document.querySelector("#js_fanyi_input");
            inputDiv.innerText = text;
            const event = new Event("input", { bubbles: true });
            inputDiv.dispatchEvent(event);
        }, content);
        
        await pageYD.keyboard.press("Enter");
        
        await pageYD.waitForFunction((lastResult) => {
            const spans = document.querySelectorAll("#js_fanyi_output_resultOutput > p > span");
            return spans && Array.from(spans).map(span => span.innerText.trim()).join("") !== lastResult;
        }, { timeout: 15000 }, lastResultYD);
        
        const result = await pageYD.evaluate(() => {
            const spans = document.querySelectorAll("#js_fanyi_output_resultOutput > p > span");
            return Array.from(spans).map(span => span.innerText.trim()).join("");
        });
        
        return result;
    }
}
```

## 9. 性能优化和最佳实践

### 9.1 性能优化策略
- **缓存机制**：翻译结果缓存，避免重复请求
- **异步处理**：非阻塞式操作，提升响应速度
- **资源管理**：及时释放图像资源和浏览器实例
- **内存优化**：限制缓存大小，防止内存泄漏

### 9.2 错误处理机制
- **网络异常**：自动重试和降级处理
- **服务异常**：友好的错误提示和日志记录
- **资源异常**：文件操作异常捕获和处理
- **用户异常**：输入验证和边界条件检查

### 9.3 安全性考虑
- **API密钥保护**：本地加密存储
- **网络安全**：HTTPS通信和证书验证
- **文件安全**：临时文件及时清理
- **权限控制**：最小权限原则

## 10. 未来发展规划

### 10.1 功能扩展计划
- **多语言支持**：增加更多OCR识别语言
- **翻译引擎**：集成更多翻译服务提供商
- **批量处理**：支持多文件批量翻译
- **历史记录**：翻译历史管理和搜索
- **云端同步**：配置和历史数据云端备份

### 10.2 技术升级方向
- **AI模型优化**：集成更先进的OCR和翻译模型
- **性能提升**：GPU加速和并行处理
- **移动端支持**：开发移动应用版本
- **Web版本**：提供在线服务版本
- **API开放**：为第三方开发者提供API接口

### 10.3 用户体验改进
- **界面优化**：更现代化的UI设计
- **交互改进**：更直观的操作流程
- **个性化设置**：更丰富的自定义选项
- **多主题支持**：深色模式和主题切换
- **无障碍支持**：提升可访问性

## 11. 项目总结

ScreenTranslator项目是一个技术先进、功能完善的屏幕翻译解决方案。项目采用现代化的技术栈，实现了高效的OCR识别和多模态翻译功能。通过模块化的架构设计，项目具有良好的可维护性和扩展性。

### 11.1 项目亮点
1. **技术创新**：结合传统翻译和AI大模型，提供多样化翻译选择
2. **用户体验**：简洁直观的界面设计，便捷的快捷键操作
3. **跨平台支持**：基于Electron实现真正的跨平台兼容
4. **高精度识别**：集成PaddleOCR，提供准确的文字识别
5. **开源生态**：遵循开源协议，便于社区贡献和改进

### 11.2 应用价值
- **教育领域**：辅助外语学习和文档理解
- **商业应用**：提升国际化软件使用效率
- **学术研究**：支持多语言文献阅读
- **日常使用**：解决跨语言交流障碍

### 11.3 技术贡献
项目展示了现代桌面应用开发的最佳实践，包括：
- Electron跨平台应用开发
- Python Flask微服务架构
- 浏览器自动化技术应用
- AI大模型集成方案
- 多进程协作和IPC通信

该项目为类似的桌面工具开发提供了优秀的参考案例，具有重要的技术参考价值和实用价值。

---

**文档版本**：v1.0
**编写日期**：2025年8月15日
**项目作者**：ScriptLin
**许可证**：ISC License
**GitHub仓库**：https://github.com/scriptLin-bjtu/ScreenTranslator
