/**
 * 车载LED显示屏控制系统前端应用
 */

class LEDDisplayApp {
    constructor() {
        this.socket = null;
        this.currentFileId = null;
        this.config = null;
        
        this.init();
    }

    /**
     * 初始化应用
     */
    async init() {
        try {
            // 初始化WebSocket连接
            this.initWebSocket();
            
            // 加载配置
            await this.loadConfig();
            
            // 绑定事件
            this.bindEvents();
            
            // 初始化UI
            this.initUI();
            
            console.log('LED显示屏应用初始化完成');
        } catch (error) {
            console.error('应用初始化失败:', error);
            this.showToast('应用初始化失败', 'error');
        }
    }

    /**
     * 初始化WebSocket连接
     */
    initWebSocket() {
        this.socket = io();
        
        this.socket.on('connect', () => {
            console.log('WebSocket连接成功');
            this.updateConnectionStatus(true);
        });

        this.socket.on('disconnect', () => {
            console.log('WebSocket连接断开');
            this.updateConnectionStatus(false);
        });

        this.socket.on('processing_complete', (data) => {
            this.handleProcessingComplete(data);
        });

        this.socket.on('error', (error) => {
            console.error('WebSocket错误:', error);
            this.showToast('连接错误: ' + error.message, 'error');
        });
    }

    /**
     * 加载配置
     */
    async loadConfig() {
        try {
            const response = await fetch('/api/config');
            const data = await response.json();
            
            if (data.success) {
                this.config = data.config;
                this.updateUIWithConfig();
            } else {
                throw new Error('加载配置失败');
            }
        } catch (error) {
            console.error('加载配置失败:', error);
            this.showToast('加载配置失败', 'error');
        }
    }

    /**
     * 根据配置更新UI
     */
    updateUIWithConfig() {
        if (!this.config) return;

        const ledConfig = this.config.led_display;
        
        // 设置默认值
        document.getElementById('ledWidth').value = ledConfig.default_width;
        document.getElementById('ledHeight').value = ledConfig.default_height;
        
        // 设置范围限制
        document.getElementById('ledWidth').max = ledConfig.max_width;
        document.getElementById('ledHeight').max = ledConfig.max_height;
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 文件上传相关
        const fileInput = document.getElementById('fileInput');
        const uploadArea = document.getElementById('uploadArea');

        fileInput.addEventListener('change', (e) => this.handleFileSelect(e));
        
        // 拖拽上传
        uploadArea.addEventListener('dragover', (e) => this.handleDragOver(e));
        uploadArea.addEventListener('dragleave', (e) => this.handleDragLeave(e));
        uploadArea.addEventListener('drop', (e) => this.handleFileDrop(e));
        uploadArea.addEventListener('click', () => fileInput.click());

        // 参数控制
        document.getElementById('brightness').addEventListener('input', (e) => {
            const value = Math.round(e.target.value * 100);
            document.getElementById('brightnessValue').textContent = value;
        });

        // 按钮事件
        document.getElementById('processBtn').addEventListener('click', () => this.processImage());
        document.getElementById('previewBtn').addEventListener('click', () => this.generatePreview());
        
        // 下载按钮
        document.getElementById('downloadProcessed').addEventListener('click', () => this.downloadFile('processed'));
        document.getElementById('downloadLED').addEventListener('click', () => this.downloadFile('led'));

        // 实时预览（参数变化时）
        const paramInputs = ['ledWidth', 'ledHeight', 'colorMode', 'brightness', 'maintainAspect'];
        paramInputs.forEach(id => {
            const element = document.getElementById(id);
            element.addEventListener('change', () => this.debouncePreview());
        });
    }

    /**
     * 初始化UI
     */
    initUI() {
        // 隐藏状态卡片
        document.getElementById('statusCard').style.display = 'none';
        document.getElementById('downloadCard').style.display = 'none';
    }

    /**
     * 处理文件选择
     */
    async handleFileSelect(event) {
        const file = event.target.files[0];
        if (file) {
            await this.uploadFile(file);
        }
    }

    /**
     * 处理拖拽悬停
     */
    handleDragOver(event) {
        event.preventDefault();
        event.stopPropagation();
        document.getElementById('uploadArea').classList.add('dragover');
    }

    /**
     * 处理拖拽离开
     */
    handleDragLeave(event) {
        event.preventDefault();
        event.stopPropagation();
        document.getElementById('uploadArea').classList.remove('dragover');
    }

    /**
     * 处理文件拖拽放置
     */
    async handleFileDrop(event) {
        event.preventDefault();
        event.stopPropagation();
        
        const uploadArea = document.getElementById('uploadArea');
        uploadArea.classList.remove('dragover');
        
        const files = event.dataTransfer.files;
        if (files.length > 0) {
            await this.uploadFile(files[0]);
        }
    }

    /**
     * 上传文件
     */
    async uploadFile(file) {
        try {
            // 验证文件类型
            if (!file.type.startsWith('image/')) {
                this.showToast('请选择图像文件', 'error');
                return;
            }

            // 显示加载状态
            this.showStatus('正在上传文件...');

            const formData = new FormData();
            formData.append('file', file);

            const response = await fetch('/api/upload', {
                method: 'POST',
                body: formData
            });

            const data = await response.json();

            if (data.success) {
                this.currentFileId = data.file_id;
                this.displayOriginalImage(file);
                this.displayFileInfo(data.image_info);
                this.enableControls();
                this.hideStatus();
                this.showToast('文件上传成功', 'success');
            } else {
                throw new Error(data.error);
            }

        } catch (error) {
            console.error('文件上传失败:', error);
            this.showToast('文件上传失败: ' + error.message, 'error');
            this.hideStatus();
        }
    }

    /**
     * 显示原始图像
     */
    displayOriginalImage(file) {
        const container = document.getElementById('originalImageContainer');
        const reader = new FileReader();
        
        reader.onload = (e) => {
            container.innerHTML = `<img src="${e.target.result}" alt="原始图像" class="fade-in">`;
        };
        
        reader.readAsDataURL(file);
    }

    /**
     * 显示文件信息
     */
    displayFileInfo(imageInfo) {
        document.getElementById('fileName').textContent = this.currentFileId;
        document.getElementById('fileSize').textContent = `${imageInfo.width} × ${imageInfo.height}`;
        document.getElementById('fileFormat').textContent = imageInfo.format;
        document.getElementById('fileInfo').style.display = 'block';
    }

    /**
     * 启用控制按钮
     */
    enableControls() {
        document.getElementById('processBtn').disabled = false;
        document.getElementById('previewBtn').disabled = false;
    }

    /**
     * 处理图像
     */
    async processImage() {
        if (!this.currentFileId) {
            this.showToast('请先上传图像文件', 'error');
            return;
        }

        try {
            this.showStatus('正在处理图像...');

            const params = this.getProcessingParams();
            
            const response = await fetch('/api/process', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    file_id: this.currentFileId,
                    ...params
                })
            });

            const data = await response.json();

            if (data.success) {
                this.displayLEDResult(data);
                this.showDownloadOptions();
                this.hideStatus();
                this.showToast('图像处理完成', 'success');
            } else {
                throw new Error(data.error);
            }

        } catch (error) {
            console.error('图像处理失败:', error);
            this.showToast('图像处理失败: ' + error.message, 'error');
            this.hideStatus();
        }
    }

    /**
     * 生成预览
     */
    async generatePreview() {
        if (!this.currentFileId) return;

        try {
            const params = this.getProcessingParams();
            
            const response = await fetch('/api/preview', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    file_id: this.currentFileId,
                    ...params
                })
            });

            const data = await response.json();

            if (data.success) {
                this.displayPreview(data.preview_base64);
                this.displayMatrixInfo(data.matrix_info);
            }

        } catch (error) {
            console.error('预览生成失败:', error);
        }
    }

    /**
     * 获取处理参数
     */
    getProcessingParams() {
        return {
            led_width: parseInt(document.getElementById('ledWidth').value),
            led_height: parseInt(document.getElementById('ledHeight').value),
            color_mode: document.getElementById('colorMode').value,
            brightness: parseFloat(document.getElementById('brightness').value),
            maintain_aspect: document.getElementById('maintainAspect').checked
        };
    }

    /**
     * 显示LED结果
     */
    displayLEDResult(data) {
        // 显示LED模拟图像
        const container = document.getElementById('ledDisplayContainer');
        container.innerHTML = `<img src="/api/output/led_${this.currentFileId}.png" alt="LED显示效果" class="fade-in">`;
        
        // 显示矩阵信息
        this.displayMatrixInfo(data.matrix_info);
    }

    /**
     * 显示预览
     */
    displayPreview(base64Data) {
        const container = document.getElementById('ledDisplayContainer');
        container.innerHTML = `<img src="data:image/png;base64,${base64Data}" alt="LED预览" class="fade-in">`;
    }

    /**
     * 显示矩阵信息
     */
    displayMatrixInfo(matrixInfo) {
        document.getElementById('matrixSize').textContent = `${matrixInfo.matrix_width} × ${matrixInfo.matrix_height}`;
        document.getElementById('totalPixels').textContent = matrixInfo.total_pixels;
        document.getElementById('ledInfo').style.display = 'block';
    }

    /**
     * 显示下载选项
     */
    showDownloadOptions() {
        document.getElementById('downloadCard').style.display = 'block';
    }

    /**
     * 下载文件
     */
    downloadFile(type) {
        if (!this.currentFileId) return;
        
        const url = `/api/download/${type}/${this.currentFileId}`;
        const link = document.createElement('a');
        link.href = url;
        link.download = '';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    /**
     * 防抖预览
     */
    debouncePreview() {
        clearTimeout(this.previewTimeout);
        this.previewTimeout = setTimeout(() => {
            this.generatePreview();
        }, 500);
    }

    /**
     * 显示状态
     */
    showStatus(message) {
        document.getElementById('statusText').textContent = message;
        document.getElementById('statusCard').style.display = 'block';
    }

    /**
     * 隐藏状态
     */
    hideStatus() {
        document.getElementById('statusCard').style.display = 'none';
    }

    /**
     * 更新连接状态
     */
    updateConnectionStatus(connected) {
        const statusElement = document.getElementById('connectionStatus');
        const icon = statusElement.querySelector('.fa-circle');
        
        if (connected) {
            icon.className = 'fas fa-circle text-success';
            statusElement.innerHTML = '<i class="fas fa-circle text-success"></i> 已连接';
        } else {
            icon.className = 'fas fa-circle text-danger';
            statusElement.innerHTML = '<i class="fas fa-circle text-danger"></i> 连接断开';
        }
    }

    /**
     * 处理处理完成事件
     */
    handleProcessingComplete(data) {
        if (data.file_id === this.currentFileId) {
            // 更新显示
            const container = document.getElementById('ledDisplayContainer');
            container.innerHTML = `<img src="${data.led_url}?t=${Date.now()}" alt="LED显示效果" class="fade-in">`;
            
            this.showToast('处理完成', 'success');
        }
    }

    /**
     * 显示Toast通知
     */
    showToast(message, type = 'info') {
        const toastBody = document.getElementById('toastBody');
        const toast = document.getElementById('toast');
        
        toastBody.textContent = message;
        
        // 设置Toast样式
        const toastHeader = toast.querySelector('.toast-header');
        toastHeader.className = `toast-header bg-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'primary'} text-white`;
        
        // 显示Toast
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new LEDDisplayApp();
});
